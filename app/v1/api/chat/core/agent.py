from app.core.security import get_tenant_info
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from app.v1.api.chat.core.tools import LegalTools
from app.v1.api.chat.core.utils.process_najirs import _process_sources

from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_mongodb import MongoDBChatMessageHistory
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages

from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from fastapi import APIRouter, Depends, Query
from typing import Annotated, List, Optional, Dict, Any
import json
import asyncio
import time
from datetime import datetime, timezone
import urllib.parse

logger = StructuredLogger(__name__)
router = APIRouter()

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    original_query: str  # Store original query
    query_analysis: Dict[str, Any]  # Query complexity and type analysis
    tools_used: List[str]  # Track tools used
    selected_tools: List[str]  # Dynamically selected tools
    najir_response: str
    act_response: str
    najir_summary_response: str
    najir_sources: List[dict]
    act_sources: List[dict]
    najir_summary_sources: List[dict]
    final_response: str
    start_time: float
    step_times: Dict[str, float]
    quality_score: float  # Quality assessment of results
    needs_refinement: bool  # Whether to refine the query
    iteration_count: int  # Track refinement iterations
    cited_response: str  # Final response with proper citations
    citation_metadata: Dict[str, Any]  # Citation tracking metadata
    cited_response: str  # Final response with proper citations
    citation_metadata: Dict[str, Any]  # Citation tracking metadata

# Global caches
_TOOL_CACHE = {}

def get_current_timestamp():
    """Get current UTC timestamp in ISO format"""
    return datetime.now(timezone.utc).isoformat()

class ChatHistoryManager:
    """Manages chat history using MongoDB with proper message structure"""
    
    def __init__(self, current_user: CurrentUser):
        self.current_user: CurrentUser = current_user
        logger.info("💬 CHAT HISTORY MANAGER - Initialized")
    
    async def get_user_history(self, user_id: str) -> MongoDBChatMessageHistory:
        """Get chat history for specific user"""
        logger.info(f"📚 GETTING CHAT HISTORY - User: {user_id}")
        
        # Construct connection string from async client
        client = self.current_user.db.client
        address = client.address
        connection_string = f"mongodb://{address[0]}:{address[1]}"
        
        return MongoDBChatMessageHistory(
            connection_string=connection_string,
            database_name=self.current_user.db.read_db.name,
            collection_name="chat_history",
            session_id=user_id
        )
    
    async def save_interaction(self, user_id: str, query: str, response: str, tools_used: List[str] = None, step_times: Dict[str, float] = None):
        """Save user interaction to chat history with proper timestamps and metadata"""
        logger.info(f"💾 SAVING INTERACTION - User: {user_id}")
        
        current_time = get_current_timestamp()
        
        # Create user message with proper structure
        user_message_data = {
            "role": "user",
            "content": query,
            "sender": "user",
            "created_at": current_time,
            "user_id": user_id,
            "message_type": "query"
        }
        
        # Create assistant message with proper structure and tools used
        assistant_message_data = {
            "role": "assistant", 
            "content": response,
            "sender": "assistant",
            "created_at": current_time,
            "tools_used": tools_used or [],
            "user_id": user_id,
            "message_type": "response",
            "step_times": step_times or {},
            "processing_metadata": {
                "total_tools": len(tools_used) if tools_used else 0,
                "workflow_completed": True
            }
        }
        
        history = await self.get_user_history(user_id)
        
        # Add messages with metadata
        history.add_message(HumanMessage(
            content=query,
            additional_kwargs=user_message_data
        ))
        
        history.add_message(AIMessage(
            content=response,
            additional_kwargs=assistant_message_data
        ))
        
        logger.info("✅ INTERACTION SAVED - Chat history updated with timestamps and metadata")

class LegalAgent:
    def __init__(self, current_user: CurrentUser):
        logger.info("🤖 AGENT INIT - Starting LegalAgent initialization")
        
        self.current_user = current_user
        self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        self.tools = []
        self.checkpointer = AsyncMongoDBSaver(
            client=current_user.db.async_client,
            db_name=current_user.db.read_db.name,
            collection_name="checkpoints"
        )
        self.chat_manager = ChatHistoryManager(current_user)
        self.graph = None
        
        self.query_analysis_prompt = """
Analyze this Nepali legal query and determine the optimal research strategy:

Query: {query}

Analyze and respond with JSON format:
{{
    "complexity": "simple|moderate|complex",
    "query_type": "case_law|statute|mixed|procedural|constitutional",
    "needs_precedents": true/false,
    "needs_statutes": true/false,
    "needs_summaries": true/false,
    "legal_domain": "civil|criminal|constitutional|commercial|family|other",
    "confidence": 0.0-1.0,
    "reasoning": "explanation of analysis"
}}

Consider:
- Simple: Direct factual questions about specific laws
- Moderate: Questions requiring interpretation or comparison
- Complex: Multi-faceted legal analysis requiring precedents and statutes
"""

        self.synthesis_prompt = """
You are given multiple legal reference responses for a user query.  

Original User Query: {query}

Sources:  
- NAJIR Response (Case Law): {najir_response}  
- ACT Response (Statutes): {act_response}  
- SUMMARY Response (Legal Summaries): {najir_summary_response}  

Task:  
- Carefully read all provided responses.  
- Synthesize them into one **comprehensive, unified legal analysis**.  
- The final answer must:  
  1. Directly address the user's query: "{query}"
  2. Stay strictly grounded in the provided sources (NAJIR, ACT, SUMMARY).  
  3. Avoid adding new interpretations, assumptions, or information not present in the sources.  
  4. Present the analysis clearly, structured, and logically connected.
  5. If sources are contradictory, acknowledge the differences.
  6. If sources are insufficient, clearly state the limitations.

Output:  
A single, consolidated legal guidance derived entirely from the provided sources in Markdown format.
Respond in the same language as the user's query.
"""
        
        logger.info("✅ AGENT INIT COMPLETE")

    def setup_tools(self):
        """Setup tools with caching"""
        user_id = str(self.current_user.user.id)
        
        if user_id not in _TOOL_CACHE:
            logger.info(f"🔨 CREATING TOOLS - For user: {user_id}")
            legal_tools = LegalTools()
            self.tools = legal_tools.create_tools(self.current_user)
            _TOOL_CACHE[user_id] = self.tools
            logger.info(f"✅ TOOLS CACHED - Cached for user: {user_id}")
        else:
            logger.info(f"♻️  USING CACHED TOOLS - For user: {user_id}")
            self.tools = _TOOL_CACHE[user_id]

    def query_analysis_node(self, state: AgentState):
        """STEP 1: Analyze query and determine optimal strategy"""
        step_start = time.time()
        logger.info("🔄 STEP 1 START - Query analysis")

        user_query = state.get("original_query", state["messages"][-1].content)
        logger.info(f"📝 USER QUERY: {user_query}")
        logger.info(f"🕐 STEP 1 TIMESTAMP: {get_current_timestamp()}")

        # Analyze query to determine strategy
        analysis_prompt = self.query_analysis_prompt.format(query=user_query)
        analysis_response = self.llm.invoke([SystemMessage(content=analysis_prompt)])

        try:
            # Parse JSON response
            query_analysis = json.loads(analysis_response.content)
            logger.info(f"� QUERY ANALYSIS: {query_analysis}")
        except json.JSONDecodeError:
            # Fallback to default analysis
            logger.warning("Failed to parse query analysis, using defaults")
            query_analysis = {
                "complexity": "moderate",
                "query_type": "mixed",
                "needs_precedents": True,
                "needs_statutes": True,
                "needs_summaries": True,
                "legal_domain": "other",
                "confidence": 0.5,
                "reasoning": "Default analysis due to parsing error"
            }

        step_time = time.time() - step_start
        step_times = state.get("step_times", {})
        step_times["query_analysis"] = step_time

        logger.info(f"✅ STEP 1 COMPLETE - Query analyzed in {step_time:.2f}s")

        return {
            "query_analysis": query_analysis,
            "step_times": step_times
        }

    def tool_selection_node(self, state: AgentState):
        """STEP 2: Dynamically select tools based on query analysis"""
        step_start = time.time()
        logger.info("🔄 STEP 2 START - Dynamic tool selection")

        query_analysis = state.get("query_analysis", {})
        selected_tools = []

        # Dynamic tool selection based on analysis
        if query_analysis.get("needs_precedents", True):
            selected_tools.append("najir_search")
            logger.info("🏛️  SELECTED: najir_search (case law needed)")

        if query_analysis.get("needs_statutes", True):
            selected_tools.append("act_search")
            logger.info("📜 SELECTED: act_search (statutes needed)")

        if query_analysis.get("needs_summaries", True) or query_analysis.get("complexity") == "complex":
            selected_tools.append("najir_summary")
            logger.info("📋 SELECTED: najir_summary (summaries needed)")

        # For simple queries, might only need one tool
        if query_analysis.get("complexity") == "simple" and len(selected_tools) > 1:
            # Prioritize based on query type
            query_type = query_analysis.get("query_type", "mixed")
            if query_type == "case_law":
                selected_tools = ["najir_search"]
            elif query_type == "statute":
                selected_tools = ["act_search"]
            logger.info(f"🎯 SIMPLIFIED: Using only {selected_tools} for simple query")

        step_time = time.time() - step_start
        step_times = state.get("step_times", {})
        step_times["tool_selection"] = step_time

        logger.info(f"✅ STEP 2 COMPLETE - Selected {len(selected_tools)} tools in {step_time:.2f}s")

        return {
            "selected_tools": selected_tools,
            "step_times": step_times
        }

    async def dynamic_tools_node(self, state: AgentState):
        """STEP 3: Execute dynamically selected tools"""
        step_start = time.time()
        logger.info("🔄 STEP 3 START - Dynamic tools execution")
        logger.info(f"🕐 STEP 3 TIMESTAMP: {get_current_timestamp()}")

        selected_tools = state.get("selected_tools", [])
        user_query = state.get("original_query", "")
        query_analysis = state.get("query_analysis", {})
        complexity = query_analysis.get("complexity", "moderate")

        if not selected_tools:
            logger.warning("⚠️  NO TOOLS SELECTED - Using default tools")
            selected_tools = ["najir_search", "act_search", "najir_summary"]

        # Setup parallel execution for selected tools only
        tasks = []
        tool_map = {}
        tools_used = []

        for tool_name in selected_tools:
            tools_used.append(tool_name)
            logger.info(f"🔧 PREPARING TOOL - {tool_name} (complexity: {complexity})")

            for tool in self.tools:
                if tool.name == tool_name:
                    task = tool.arun({"query": user_query, "complexity": complexity})
                    tasks.append(task)
                    tool_map[len(tasks) - 1] = tool_name
                    break

        logger.info(f"🚀 EXECUTING DYNAMIC - {len(tasks)} selected tools running")
        execution_start = time.time()

        # Execute selected tools in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)

        execution_time = time.time() - execution_start
        logger.info(f"⚡ DYNAMIC EXECUTION COMPLETE - {execution_time:.2f}s")

        # Process results
        najir_response = act_response = najir_summary_response = ""
        najir_sources = act_sources = najir_summary_sources = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ TOOL ERROR - Index {i}: {result}")
                continue

            try:
                parsed_result = json.loads(result)
                tool_name = tool_map[i]

                if tool_name == "najir_search":
                    najir_response = parsed_result.get('response', '')
                    najir_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 NAJIR RESULTS - Response length: {len(najir_response)}, Sources: {len(najir_sources)}")
                elif tool_name == "act_search":
                    act_response = parsed_result.get('response', '')
                    act_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 ACT RESULTS - Response length: {len(act_response)}, Sources: {len(act_sources)}")
                elif tool_name == "najir_summary":
                    najir_summary_response = parsed_result.get('response', '')
                    najir_summary_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 SUMMARY RESULTS - Response length: {len(najir_summary_response)}, Sources: {len(najir_summary_sources)}")

            except Exception as e:
                logger.error(f"❌ RESULT PROCESSING ERROR - {e}")
        
        step_time = time.time() - step_start
        logger.info(f"✅ STEP 3 COMPLETE - All tools processed in {step_time:.2f}s")

        # Update step times and tools used
        step_times = state.get("step_times", {})
        step_times["dynamic_tools"] = step_time

        return {
            "tools_used": tools_used,
            "najir_response": najir_response,
            "act_response": act_response,
            "najir_summary_response": najir_summary_response,
            "najir_sources": najir_sources,
            "act_sources": act_sources,
            "najir_summary_sources": najir_summary_sources,
            "step_times": step_times
        }

    def quality_assessment_node(self, state: AgentState):
        """STEP 4: Assess quality of retrieved results"""
        step_start = time.time()
        logger.info("🔄 STEP 4 START - Quality assessment")

        # Calculate quality score based on multiple factors
        najir_sources = state.get("najir_sources", [])
        act_sources = state.get("act_sources", [])
        summary_sources = state.get("najir_summary_sources", [])

        total_sources = len(najir_sources) + len(act_sources) + len(summary_sources)

        # Base quality score
        quality_score = 0.3

        # Source count factor (more sources = higher quality)
        if total_sources >= 5:
            quality_score += 0.3
        elif total_sources >= 3:
            quality_score += 0.2
        elif total_sources >= 1:
            quality_score += 0.1

        # Source diversity factor
        source_types = sum([
            1 if najir_sources else 0,
            1 if act_sources else 0,
            1 if summary_sources else 0
        ])
        quality_score += source_types * 0.1

        # Response quality factor (check if responses are meaningful)
        responses = [
            state.get("najir_response", ""),
            state.get("act_response", ""),
            state.get("najir_summary_response", "")
        ]
        meaningful_responses = sum(1 for r in responses if len(r.strip()) > 50)
        quality_score += meaningful_responses * 0.05

        quality_score = min(1.0, quality_score)

        # Determine if refinement is needed
        needs_refinement = quality_score < 0.6 and state.get("iteration_count", 0) < 2

        step_time = time.time() - step_start
        step_times = state.get("step_times", {})
        step_times["quality_assessment"] = step_time

        logger.info(f"📊 QUALITY SCORE: {quality_score:.2f}")
        logger.info(f"🔄 NEEDS REFINEMENT: {needs_refinement}")
        logger.info(f"✅ STEP 4 COMPLETE - Quality assessed in {step_time:.2f}s")

        return {
            "quality_score": quality_score,
            "needs_refinement": needs_refinement,
            "step_times": step_times
        }

    def synthesis_node(self, state: AgentState):
        """STEP 5: Intelligently synthesize responses based on quality and available data"""
        step_start = time.time()
        logger.info("🔄 STEP 5 START - Intelligent response synthesis")
        logger.info(f"🕐 STEP 5 TIMESTAMP: {get_current_timestamp()}")

        user_query = state.get("original_query", "")
        quality_score = state.get("quality_score", 0.5)
        query_analysis = state.get("query_analysis", {})

        # Adaptive synthesis based on available responses
        available_responses = []
        if state.get("najir_response"):
            available_responses.append(f"Case Law Analysis: {state.get('najir_response')}")
        if state.get("act_response"):
            available_responses.append(f"Statutory Analysis: {state.get('act_response')}")
        if state.get("najir_summary_response"):
            available_responses.append(f"Legal Summary: {state.get('najir_summary_response')}")

        # Enhanced synthesis prompt based on query complexity
        complexity = query_analysis.get("complexity", "moderate")
        legal_domain = query_analysis.get("legal_domain", "general")

        enhanced_synthesis_prompt = f"""
You are a legal expert providing comprehensive analysis for Nepali law.

Original Query: {user_query}
Query Complexity: {complexity}
Legal Domain: {legal_domain}
Quality Score: {quality_score:.2f}

Available Legal Sources:
{chr(10).join(available_responses) if available_responses else "Limited sources available"}

Instructions:
1. Provide a direct, comprehensive answer to the user's query
2. Structure your response clearly with proper legal reasoning
3. Reference specific legal provisions or precedents when available
4. If quality is low ({quality_score:.2f}), acknowledge limitations
5. Use proper Nepali legal terminology where appropriate
6. Respond in the same language as the user's query

Provide a well-structured legal analysis that addresses the query comprehensively.
"""

        logger.info(f"🎯 SYNTHESIZING RESPONSE - Complexity: {complexity}, Quality: {quality_score:.2f}")

        synthesis_start = time.time()
        final_response = self.llm.invoke([SystemMessage(content=enhanced_synthesis_prompt)])
        synthesis_time = time.time() - synthesis_start

        step_time = time.time() - step_start

        logger.info(f"🎯 SYNTHESIS COMPLETE - Generated in {synthesis_time:.2f}s")
        logger.info(f"📝 FINAL RESPONSE LENGTH - {len(final_response.content)} characters")
        logger.info(f"✅ STEP 5 COMPLETE - Final response ready in {step_time:.2f}s")
        
        # Update step times
        step_times = state.get("step_times", {})
        step_times["synthesis"] = step_time
        
        # Create final message with metadata
        final_message = AIMessage(
            content=final_response.content,
            additional_kwargs={
                "role": "assistant",
                "sender": "assistant",
                "created_at": get_current_timestamp(),
                "message_type": "final_response",
                "tools_used": state.get("tools_used", []),
                "step_times": step_times,
                "synthesis_time": synthesis_time,
                "total_sources": {
                    "najir": len(state.get("najir_sources", [])),
                    "act": len(state.get("act_sources", [])),
                    "summary": len(state.get("najir_summary_sources", []))
                }
            }
        )
        
        return {
            "messages": [final_message],
            "final_response": final_response.content,
            "step_times": step_times
        }

    def citation_refinement_node(self, state: AgentState):
        """STEP 6: Add proper citations and refine the final response"""
        step_start = time.time()
        logger.info("🔄 STEP 6 START - Citation refinement")
        logger.info(f"🕐 STEP 6 TIMESTAMP: {get_current_timestamp()}")

        final_response = state.get("final_response", "")
        user_query = state.get("original_query", "")

        # Collect all sources with proper legal citation formatting
        all_sources = []

        def get_source_identifier(source_type, metadata):
            """Get the actual source identifier for direct citation"""

            if source_type == "najir":
                # Get najir article ID
                article_id = metadata.get("article_id", metadata.get("id", ""))
                if article_id:
                    return f"Najir({article_id})"
                else:
                    # Fallback to file name or generic
                    file_name = metadata.get("file_name", "")
                    return f"Najir({file_name})" if file_name else "Najir(unknown)"

            elif source_type == "act":
                # Get act ID or name
                act_id = metadata.get("act_id", metadata.get("id", ""))
                act_name = metadata.get("title", metadata.get("act_name", ""))

                if act_id:
                    return f"Act({act_id})"
                elif act_name:
                    # Use short form of act name
                    short_name = act_name.split()[0] if act_name else "Act"
                    return f"Act({short_name})"
                else:
                    file_name = metadata.get("file_name", "")
                    return f"Act({file_name})" if file_name else "Act(unknown)"

            elif source_type == "summary":
                # Get summary ID or identifier
                summary_id = metadata.get("summary_id", metadata.get("id", ""))
                if summary_id:
                    return f"Summary({summary_id})"
                else:
                    file_name = metadata.get("file_name", "")
                    return f"Summary({file_name})" if file_name else "Summary(unknown)"

            return "Source(unknown)"

        def format_source_description(source_type, metadata):
            """Format source description for the sources list"""

            if source_type == "najir":
                article_id = metadata.get("article_id", metadata.get("id", ""))
                case_name = metadata.get("title", metadata.get("case_name", ""))
                court = metadata.get("court", "")
                year = metadata.get("year", metadata.get("date", ""))

                description = f"Najir Article {article_id}" if article_id else "Najir Case"
                if case_name:
                    description += f": {case_name}"
                if court:
                    description += f" ({court})"
                if year:
                    description += f" [{year}]"

                return description

            elif source_type == "act":
                act_name = metadata.get("title", metadata.get("act_name", ""))
                section = metadata.get("section", metadata.get("article", ""))
                year = metadata.get("year", metadata.get("date", ""))

                description = act_name or "Legal Act"
                if section:
                    description += f", Section {section}"
                if year:
                    description += f" ({year})"

                return description

            elif source_type == "summary":
                title = metadata.get("title", metadata.get("summary_title", ""))
                source_doc = metadata.get("source_document", metadata.get("original_source", ""))

                description = f"Summary: {title}" if title else "Legal Summary"
                if source_doc:
                    description += f" (from {source_doc})"

                return description

            return "Legal Source"

        # Process najir sources
        najir_sources = state.get("najir_sources", [])
        for source in najir_sources:
            metadata = source.get("metadata", {})
            source_id = get_source_identifier("najir", metadata)
            description = format_source_description("najir", metadata)

            all_sources.append({
                "source_id": source_id,
                "type": "Case Law (Najir)",
                "description": description,
                "text": source.get("text", ""),
                "metadata": metadata,
                "score": source.get("score", 0.0)
            })

        # Process act sources
        act_sources = state.get("act_sources", [])
        for source in act_sources:
            metadata = source.get("metadata", {})
            source_id = get_source_identifier("act", metadata)
            description = format_source_description("act", metadata)

            all_sources.append({
                "source_id": source_id,
                "type": "Statute (Act)",
                "description": description,
                "text": source.get("text", ""),
                "metadata": metadata,
                "score": source.get("score", 0.0)
            })

        # Process summary sources
        summary_sources = state.get("najir_summary_sources", [])
        for source in summary_sources:
            metadata = source.get("metadata", {})
            source_id = get_source_identifier("summary", metadata)
            description = format_source_description("summary", metadata)

            all_sources.append({
                "source_id": source_id,
                "type": "Legal Summary",
                "description": description,
                "text": source.get("text", ""),
                "metadata": metadata,
                "score": source.get("score", 0.0)
            })

        # Create citation prompt with actual source identifiers
        sources_text = ""
        for source in all_sources:
            sources_text += f"{source['source_id']} - {source['description']}\n"
            sources_text += f"Type: {source['type']}\n"
            sources_text += f"Content: {source['text'][:400]}...\n"
            sources_text += f"Relevance Score: {source['score']:.2f}\n"

            # Add key metadata for context
            metadata = source.get("metadata", {})
            if metadata.get("article_id"):
                sources_text += f"Article ID: {metadata['article_id']}\n"
            if metadata.get("section"):
                sources_text += f"Section: {metadata['section']}\n"
            if metadata.get("court"):
                sources_text += f"Court: {metadata['court']}\n"
            sources_text += "\n"

        citation_prompt = f"""
You are a legal expert tasked with refining a legal response and adding proper Nepali legal citations.

Original Query: {user_query}

Current Response (without proper citations):
{final_response}

Available Legal Sources:
{sources_text}

Instructions:
1. Refine the response to be more comprehensive and accurate
2. Add proper citations using the EXACT source identifiers shown above (e.g., [Najir(12345)], [Act(Property2018)], [Summary(123)])
3. Ensure each factual claim is supported by a relevant source
4. Add a "स्रोतहरू (Sources)" section at the end listing all cited sources
5. Maintain the same language as the original query (Nepali/English)
6. Keep the legal analysis structure clear and professional
7. Only cite sources that are actually relevant to the claims made
8. Use the EXACT source identifier format provided above

Format for Sources section:
## स्रोतहरू (Sources)
[Najir(article_id)] - [Description as provided above]
[Act(act_id)] - [Description as provided above]
[Summary(summary_id)] - [Description as provided above]

Examples of proper citation usage:
- "संविधानको धारा १५ अनुसार..." [Act(Constitution2015)]
- "सर्वोच्च अदालतको फैसलामा उल्लेख छ..." [Najir(12345)]
- "सम्पत्ति ऐन २०७५ को दफा ८ मा..." [Act(Property2018)]

IMPORTANT:
- Use the EXACT source identifiers shown in the sources list above
- Do NOT use numbered citations like [1], [2], [3]
- Use the actual source IDs like [Najir(12345)], [Act(Property2018)], etc.

Provide the refined response with proper citations:
"""

        logger.info(f"🔗 ADDING CITATIONS - Processing {len(all_sources)} sources")

        citation_start = time.time()
        cited_response = self.llm.invoke([SystemMessage(content=citation_prompt)])
        citation_time = time.time() - citation_start

        step_time = time.time() - step_start

        # Create citation metadata with actual source identifiers
        citation_metadata = {
            "total_sources": len(all_sources),
            "source_breakdown": {
                "najir_case_law": len(najir_sources),
                "act_statutes": len(act_sources),
                "legal_summaries": len(summary_sources)
            },
            "citation_time": citation_time,
            "sources_processed": [
                {
                    "source_id": source["source_id"],
                    "type": source["type"],
                    "description": source["description"],
                    "metadata": {
                        "article_id": source.get("metadata", {}).get("article_id", ""),
                        "act_id": source.get("metadata", {}).get("act_id", ""),
                        "summary_id": source.get("metadata", {}).get("summary_id", ""),
                        "section": source.get("metadata", {}).get("section", ""),
                        "court": source.get("metadata", {}).get("court", ""),
                        "year": source.get("metadata", {}).get("year", ""),
                        "file_name": source.get("metadata", {}).get("file_name", "")
                    },
                    "score": source["score"]
                }
                for source in all_sources
            ]
        }

        step_times = state.get("step_times", {})
        step_times["citation_refinement"] = step_time

        logger.info(f"🔗 CITATION COMPLETE - Added citations in {citation_time:.2f}s")
        logger.info(f"📚 SOURCES CITED - {len(all_sources)} sources processed")
        logger.info(f"✅ STEP 6 COMPLETE - Citation refinement ready in {step_time:.2f}s")

        return {
            "cited_response": cited_response.content,
            "citation_metadata": citation_metadata,
            "step_times": step_times
        }

    def should_refine_query(self, state: AgentState) -> str:
        """Conditional logic to determine if query needs refinement"""
        needs_refinement = state.get("needs_refinement", False)
        iteration_count = state.get("iteration_count", 0)

        if needs_refinement and iteration_count < 2:
            logger.info("🔄 ROUTING: Query needs refinement")
            return "query_analysis"
        else:
            logger.info("✅ ROUTING: Proceeding to synthesis")
            return "synthesis"

    def should_add_citations(self, state: AgentState) -> str:
        """Conditional logic to determine if citations should be added"""
        # Always add citations as final step (state parameter required by LangGraph)
        logger.info("🔗 ROUTING: Adding citations")
        return "citation_refinement"

    def build_graph(self):
        """Build dynamic 6-step workflow graph with citation refinement"""
        logger.info("🏗️  BUILDING WORKFLOW - Creating dynamic 6-step graph with citations")

        builder = StateGraph(AgentState)

        # Add all nodes
        builder.add_node("query_analysis", self.query_analysis_node)
        builder.add_node("tool_selection", self.tool_selection_node)
        builder.add_node("dynamic_tools", self.dynamic_tools_node)
        builder.add_node("quality_assessment", self.quality_assessment_node)
        builder.add_node("synthesis", self.synthesis_node)
        builder.add_node("citation_refinement", self.citation_refinement_node)

        # Build the workflow
        builder.add_edge(START, "query_analysis")
        builder.add_edge("query_analysis", "tool_selection")
        builder.add_edge("tool_selection", "dynamic_tools")
        builder.add_edge("dynamic_tools", "quality_assessment")

        # Conditional edge for refinement
        builder.add_conditional_edges(
            "quality_assessment",
            self.should_refine_query,
            {
                "query_analysis": "query_analysis",  # Refine if quality is low
                "synthesis": "synthesis"  # Proceed if quality is good
            }
        )

        # Always add citations after synthesis
        builder.add_conditional_edges(
            "synthesis",
            self.should_add_citations,
            {
                "citation_refinement": "citation_refinement"
            }
        )

        self.graph = builder.compile(checkpointer=self.checkpointer)

        logger.info("✅ WORKFLOW BUILT - Dynamic 6-step graph ready with citation refinement")
        return self.graph

    async def run(self, query: str, thread_id: Optional[str] = None):
        """Execute 3-step workflow with MongoDB persistence and proper timestamps"""
        workflow_start = time.time()
        user_id = str(self.current_user.user.id)
        
        logger.info("🚀 WORKFLOW START - Beginning legal research")
        logger.info(f"🕐 WORKFLOW TIMESTAMP: {get_current_timestamp()}")
        logger.info(f"👤 USER: {user_id}")
        logger.info(f"🔗 THREAD: {thread_id or user_id}")
        logger.info(f"❓ QUERY: {query}")
        
        if not self.tools:
            self.setup_tools()
        if not self.graph:
            self.build_graph()
        
        config = {"configurable": {"thread_id": thread_id or user_id}}
        
        initial_state = {
            "messages": [HumanMessage(
                content=query,
                additional_kwargs={
                    "role": "user",
                    "sender": "user",
                    "created_at": get_current_timestamp(),
                    "user_id": user_id,
                    "message_type": "query"
                }
            )],
            "original_query": query,
            "query_analysis": {},
            "tools_used": [],
            "selected_tools": [],
            "najir_response": "",
            "act_response": "",
            "najir_summary_response": "",
            "najir_sources": [],
            "act_sources": [],
            "najir_summary_sources": [],
            "final_response": "",
            "start_time": workflow_start,
            "step_times": {},
            "quality_score": 0.0,
            "needs_refinement": False,
            "iteration_count": 0,
            "cited_response": "",
            "citation_metadata": {}
        }
        
        # Execute workflow
        result = await self.graph.ainvoke(initial_state, config=config)
        
        workflow_time = time.time() - workflow_start
        
        # Save to chat history with all metadata
        final_response = result.get("final_response", "")
        tools_used = result.get("tools_used", [])
        step_times = result.get("step_times", {})
        step_times["total_workflow"] = workflow_time
        
        await self.chat_manager.save_interaction(
            user_id=user_id, 
            query=query, 
            response=final_response,
            tools_used=tools_used,
            step_times=step_times
        )
        
        logger.info(f"🏁 WORKFLOW COMPLETE - Total time: {workflow_time:.2f}s")
        logger.info(f"🕐 WORKFLOW END TIMESTAMP: {get_current_timestamp()}")
        logger.info(f"🛠️  TOOLS USED: {', '.join(tools_used)}")
        
        return result

@router.post("/agent")
async def legal_agent_chat(
    query: str = Query(..., description="The legal query to process"),
    thread_id: Optional[str] = Query(None, description="Optional thread ID for conversation continuity"),
    current_user: CurrentUser = Depends(get_tenant_info)
):
    """
    Legal research agent with MongoDB persistence and proper message structure
    
    - **query**: The legal question or query in Nepali or English
    - **thread_id**: Optional thread ID for maintaining conversation context
    """
    request_start = time.time()
    request_timestamp = get_current_timestamp()
    
    logger.info("📡 API REQUEST START")
    logger.info(f"🕐 REQUEST TIMESTAMP: {request_timestamp}")
    logger.info(f"👤 USER: {current_user.user.id}")
    logger.info(f"❓ QUERY: {query}")
    logger.info(f"🔗 THREAD: {thread_id}")
    
    try:
        # Decode URL-encoded query if needed
        decoded_query = urllib.parse.unquote(query)
        logger.info(f"🔤 DECODED QUERY: {decoded_query}")
        
        agent = LegalAgent(current_user)
        result = await agent.run(decoded_query, thread_id)
        
        request_time = time.time() - request_start
        
        # Process najir sources
        najir_sources: Dict[str, Any] = await _process_sources(
            result.get("najir_sources", []), 
            current_user
        )
        
        response_data = {
            "final_response": result.get("cited_response", result.get("final_response", "")),  # Use cited response if available
            "cited_response": result.get("cited_response", ""),  # Separate field for cited response
            "original_response": result.get("final_response", ""),  # Keep original for comparison
            "najir_response": result.get("najir_response", ""),
            "act_response": result.get("act_response", ""),
            "najir_summary_response": result.get("najir_summary_response", ""),
            "najir_sources": najir_sources.get("sources", []),
            "act_sources": result.get("act_sources", []),
            "najir_summary_sources": result.get("najir_summary_sources", []),
            "suggested_sources": result.get("najir_summary_sources", []),
            "total_filtered": len(result.get("najir_sources", [])) + len(result.get("act_sources", [])),
            "total_suggestions": len(result.get("najir_summary_sources", [])),
            "tools_used": result.get("tools_used", []),
            "step_times": result.get("step_times", {}),
            "citation_metadata": result.get("citation_metadata", {}),
            "metadata": {
                "request_timestamp": request_timestamp,
                "response_timestamp": get_current_timestamp(),
                "total_request_time": request_time,
                "user_id": str(current_user.user.id),
                "thread_id": thread_id,
                "query_decoded": decoded_query,
                "workflow_completed": True
            },
            "status": "success"
        }
        
        logger.info(f"✅ API SUCCESS - Request completed in {request_time:.2f}s")
        logger.info(f"🕐 RESPONSE TIMESTAMP: {get_current_timestamp()}")
        
        return response_data
        
    except Exception as e:
        request_time = time.time() - request_start
        error_timestamp = get_current_timestamp()
        
        logger.error(f"❌ API ERROR - Failed after {request_time:.2f}s: {e}")
        logger.error(f"🕐 ERROR TIMESTAMP: {error_timestamp}")
        
        return {
            "final_response": f"Error processing your request: {str(e)}",
            "cited_response": "",
            "original_response": "",
            "najir_response": "",
            "act_response": "",
            "najir_sources": [],
            "act_sources": [],
            "suggested_sources": [],
            "total_filtered": 0,
            "total_suggestions": 0,
            "tools_used": [],
            "step_times": {},
            "citation_metadata": {},
            "metadata": {
                "request_timestamp": request_timestamp,
                "error_timestamp": error_timestamp,
                "total_request_time": request_time,
                "user_id": str(current_user.user.id),
                "thread_id": thread_id,
                "query_decoded": urllib.parse.unquote(query),
                "workflow_completed": False,
                "error": str(e)
            },
            "status": "error"
        }