from app.core.security import get_tenant_info
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from app.v1.api.chat.core.tools import LegalTools
from app.v1.api.chat.core.utils.process_najirs import _process_sources

from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_mongodb import MongoDBChatMessageHistory
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import tools_condition
from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from fastapi import APIRouter, Depends, Query
from typing import Annotated, List, Optional, Dict, Any
import json
import asyncio
import time
from datetime import datetime, timezone
import urllib.parse

logger = StructuredLogger(__name__)
router = APIRouter()

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    original_query: str  # Store original query
    tools_used: List[str]  # Track tools used
    najir_response: str
    act_response: str  
    najir_summary_response: str
    najir_sources: List[dict]
    act_sources: List[dict]
    najir_summary_sources: List[dict]
    final_response: str
    start_time: float
    step_times: Dict[str, float]

# Global caches
_TOOL_CACHE = {}

def get_current_timestamp():
    """Get current UTC timestamp in ISO format"""
    return datetime.now(timezone.utc).isoformat()

class ChatHistoryManager:
    """Manages chat history using MongoDB with proper message structure"""
    
    def __init__(self, current_user: CurrentUser):
        self.current_user: CurrentUser = current_user
        logger.info("💬 CHAT HISTORY MANAGER - Initialized")
    
    async def get_user_history(self, user_id: str) -> MongoDBChatMessageHistory:
        """Get chat history for specific user"""
        logger.info(f"📚 GETTING CHAT HISTORY - User: {user_id}")
        
        # Construct connection string from async client
        client = self.current_user.db.client
        address = client.address
        connection_string = f"mongodb://{address[0]}:{address[1]}"
        
        return MongoDBChatMessageHistory(
            connection_string=connection_string,
            database_name=self.current_user.db.read_db.name,
            collection_name="chat_history",
            session_id=user_id
        )
    
    async def save_interaction(self, user_id: str, query: str, response: str, tools_used: List[str] = None, step_times: Dict[str, float] = None):
        """Save user interaction to chat history with proper timestamps and metadata"""
        logger.info(f"💾 SAVING INTERACTION - User: {user_id}")
        
        current_time = get_current_timestamp()
        
        # Create user message with proper structure
        user_message_data = {
            "role": "user",
            "content": query,
            "sender": "user",
            "created_at": current_time,
            "user_id": user_id,
            "message_type": "query"
        }
        
        # Create assistant message with proper structure and tools used
        assistant_message_data = {
            "role": "assistant", 
            "content": response,
            "sender": "assistant",
            "created_at": current_time,
            "tools_used": tools_used or [],
            "user_id": user_id,
            "message_type": "response",
            "step_times": step_times or {},
            "processing_metadata": {
                "total_tools": len(tools_used) if tools_used else 0,
                "workflow_completed": True
            }
        }
        
        history = await self.get_user_history(user_id)
        
        # Add messages with metadata
        history.add_message(HumanMessage(
            content=query,
            additional_kwargs=user_message_data
        ))
        
        history.add_message(AIMessage(
            content=response,
            additional_kwargs=assistant_message_data
        ))
        
        logger.info("✅ INTERACTION SAVED - Chat history updated with timestamps and metadata")

class LegalAgent:
    def __init__(self, current_user: CurrentUser):
        logger.info("🤖 AGENT INIT - Starting LegalAgent initialization")
        
        self.current_user = current_user
        self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        self.tools = []
        self.checkpointer = AsyncMongoDBSaver(
            client=current_user.db.async_client,
            db_name=current_user.db.read_db.name,
            collection_name="checkpoints"
        )
        self.chat_manager = ChatHistoryManager(current_user)
        self.graph = None
        
        self.query_refine_prompt = """
Refine user query for Nepali legal research. Translate English to Nepali if needed.  
Create optimized queries for:  
- najir_search (case law)  
- act_search (statutes)  
- najir_summary (summaries)  

User Query: {query}  

Instructions:  
- Do NOT change or rephrase the original user query itself.  
- Only refine it for better searchability while keeping meaning intact.  
- You MUST generate refined queries for all three tools.  
- Use proper Nepali legal terminology where applicable.  
"""

        self.synthesis_prompt = """
You are given multiple legal reference responses for a user query.  

Original User Query: {query}

Sources:  
- NAJIR Response (Case Law): {najir_response}  
- ACT Response (Statutes): {act_response}  
- SUMMARY Response (Legal Summaries): {najir_summary_response}  

Task:  
- Carefully read all provided responses.  
- Synthesize them into one **comprehensive, unified legal analysis**.  
- The final answer must:  
  1. Directly address the user's query: "{query}"
  2. Stay strictly grounded in the provided sources (NAJIR, ACT, SUMMARY).  
  3. Avoid adding new interpretations, assumptions, or information not present in the sources.  
  4. Present the analysis clearly, structured, and logically connected.
  5. If sources are contradictory, acknowledge the differences.
  6. If sources are insufficient, clearly state the limitations.

Output:  
A single, consolidated legal guidance derived entirely from the provided sources in Markdown format.
Respond in the same language as the user's query.
"""
        
        logger.info("✅ AGENT INIT COMPLETE")

    def setup_tools(self):
        """Setup tools with caching"""
        user_id = str(self.current_user.user.id)
        
        if user_id not in _TOOL_CACHE:
            logger.info(f"🔨 CREATING TOOLS - For user: {user_id}")
            legal_tools = LegalTools()
            self.tools = legal_tools.create_tools(self.current_user)
            _TOOL_CACHE[user_id] = self.tools
            logger.info(f"✅ TOOLS CACHED - Cached for user: {user_id}")
        else:
            logger.info(f"♻️  USING CACHED TOOLS - For user: {user_id}")
            self.tools = _TOOL_CACHE[user_id]

    def query_refine_node(self, state: AgentState):
        """STEP 1: Query refinement and tool preparation"""
        step_start = time.time()
        logger.info("🔄 STEP 1 START - Query refinement")
        
        # Use original_query from state for consistency
        user_query = state.get("original_query", state["messages"][-1].content)
        logger.info(f"📝 USER QUERY: {user_query}")
        logger.info(f"🕐 STEP 1 TIMESTAMP: {get_current_timestamp()}")
        
        system_msg = SystemMessage(content=self.query_refine_prompt.format(query=user_query))
        
        # Create a fresh message list with just system and user message
        messages = [system_msg, HumanMessage(content=user_query)]
        logger.info(f"📜 MESSAGES FOR REFINEMENT: {len(messages)} messages prepared")
        
        llm_with_tools = self.llm.bind_tools(self.tools)
        response = llm_with_tools.invoke(messages)
        
        tool_count = len(getattr(response, 'tool_calls', []))
        step_time = time.time() - step_start
        
        logger.info(f"🛠️  STEP 1 COMPLETE - {tool_count} tool calls prepared in {step_time:.2f}s")
        
        # Update step times
        step_times = state.get("step_times", {})
        step_times["query_refine"] = step_time
        
        return {
            "messages": [response],
            "step_times": step_times
        }

    async def parallel_tools_node(self, state: AgentState):
        """STEP 2: Execute all tools in parallel"""
        step_start = time.time()
        logger.info("🔄 STEP 2 START - Parallel tools execution")
        logger.info(f"🕐 STEP 2 TIMESTAMP: {get_current_timestamp()}")
        
        last_message = state["messages"][-1]
        if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
            logger.warning("⚠️  NO TOOL CALLS - Skipping parallel execution")
            return {"messages": []}

        # Setup parallel execution
        tasks = []
        tool_call_map = {}
        tools_used = []
        
        for tool_call in last_message.tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call["args"]
            tool_id = tool_call["id"]
            
            tools_used.append(tool_name)
            logger.info(f"🔧 PREPARING TOOL - {tool_name} with args: {tool_args}")
            
            for tool in self.tools:
                if tool.name == tool_name:
                    task = tool.arun(tool_args)
                    tasks.append(task)
                    tool_call_map[len(tasks) - 1] = (tool_name, tool_id)
                    break

        logger.info(f"🚀 EXECUTING PARALLEL - {len(tasks)} tools running")
        execution_start = time.time()
        
        # Execute all tools in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        execution_time = time.time() - execution_start
        logger.info(f"⚡ PARALLEL EXECUTION COMPLETE - {execution_time:.2f}s")

        # Process results
        najir_response = act_response = najir_summary_response = ""
        najir_sources = act_sources = najir_summary_sources = []
        tool_messages = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ TOOL ERROR - Index {i}: {result}")
                continue
                
            try:
                parsed_result = json.loads(result)
                tool_name, tool_id = tool_call_map[i]
                
                if tool_name == "najir_search":
                    najir_response = parsed_result.get('response', '')
                    najir_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 NAJIR RESULTS - Response length: {len(najir_response)}, Sources: {len(najir_sources)}")
                elif tool_name == "act_search":
                    act_response = parsed_result.get('response', '')
                    act_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 ACT RESULTS - Response length: {len(act_response)}, Sources: {len(act_sources)}")
                elif tool_name == "najir_summary":
                    najir_summary_response = parsed_result.get('response', '')
                    najir_summary_sources = parsed_result.get('sources', [])
                    logger.info(f"📊 SUMMARY RESULTS - Response length: {len(najir_summary_response)}, Sources: {len(najir_summary_sources)}")
                
                from langchain_core.messages import ToolMessage
                tool_messages.append(ToolMessage(
                    content=f"{tool_name} completed successfully",
                    tool_call_id=tool_id,
                    additional_kwargs={
                        "tool_name": tool_name,
                        "executed_at": get_current_timestamp(),
                        "result_summary": {
                            "response_length": len(parsed_result.get('response', '')),
                            "sources_count": len(parsed_result.get('sources', []))
                        }
                    }
                ))
            except Exception as e:
                logger.error(f"❌ RESULT PROCESSING ERROR - {e}")
        
        step_time = time.time() - step_start
        logger.info(f"✅ STEP 2 COMPLETE - All tools processed in {step_time:.2f}s")
        
        # Update step times and tools used
        step_times = state.get("step_times", {})
        step_times["parallel_tools"] = step_time
        
        return {
            "messages": tool_messages,
            "tools_used": tools_used,
            "najir_response": najir_response,
            "act_response": act_response,
            "najir_summary_response": najir_summary_response,
            "najir_sources": najir_sources,
            "act_sources": act_sources,
            "najir_summary_sources": najir_summary_sources,
            "step_times": step_times
        }

    def synthesis_node(self, state: AgentState):
        """STEP 3: Synthesize all responses"""
        step_start = time.time()
        logger.info("🔄 STEP 3 START - Response synthesis")
        logger.info(f"🕐 STEP 3 TIMESTAMP: {get_current_timestamp()}")
        
        # Use original_query for consistency
        user_query = state.get("original_query", "")
        
        synthesis_prompt = self.synthesis_prompt.format(
            query=user_query,
            najir_response=state.get("najir_response", ""),
            act_response=state.get("act_response", ""),
            najir_summary_response=state.get("najir_summary_response", "")
        )
        
        logger.info(f"🎯 SYNTHESIZING RESPONSE - Query: {user_query[:100]}...")
        
        synthesis_start = time.time()
        final_response = self.llm.invoke([SystemMessage(content=synthesis_prompt)])
        synthesis_time = time.time() - synthesis_start
        
        step_time = time.time() - step_start
        
        logger.info(f"🎯 SYNTHESIS COMPLETE - Generated in {synthesis_time:.2f}s")
        logger.info(f"📝 FINAL RESPONSE LENGTH - {len(final_response.content)} characters")
        logger.info(f"✅ STEP 3 COMPLETE - Final response ready in {step_time:.2f}s")
        
        # Update step times
        step_times = state.get("step_times", {})
        step_times["synthesis"] = step_time
        
        # Create final message with metadata
        final_message = AIMessage(
            content=final_response.content,
            additional_kwargs={
                "role": "assistant",
                "sender": "assistant",
                "created_at": get_current_timestamp(),
                "message_type": "final_response",
                "tools_used": state.get("tools_used", []),
                "step_times": step_times,
                "synthesis_time": synthesis_time,
                "total_sources": {
                    "najir": len(state.get("najir_sources", [])),
                    "act": len(state.get("act_sources", [])),
                    "summary": len(state.get("najir_summary_sources", []))
                }
            }
        )
        
        return {
            "messages": [final_message],
            "final_response": final_response.content,
            "step_times": step_times
        }

    def build_graph(self):
        """Build 3-step workflow graph"""
        logger.info("🏗️  BUILDING WORKFLOW - Creating 3-step graph")
        
        builder = StateGraph(AgentState)
        
        builder.add_node("query_refine", self.query_refine_node)
        builder.add_node("parallel_tools", self.parallel_tools_node)
        builder.add_node("synthesis", self.synthesis_node)
        
        builder.add_edge(START, "query_refine")
        builder.add_conditional_edges(
            "query_refine",
            tools_condition,
            {"tools": "parallel_tools", "__end__": "synthesis"}
        )
        builder.add_edge("parallel_tools", "synthesis")
        
        self.graph = builder.compile(checkpointer=self.checkpointer)
        
        logger.info("✅ WORKFLOW BUILT - 3-step graph ready with MongoDB checkpoints")
        return self.graph

    async def run(self, query: str, thread_id: Optional[str] = None):
        """Execute 3-step workflow with MongoDB persistence and proper timestamps"""
        workflow_start = time.time()
        user_id = str(self.current_user.user.id)
        
        logger.info("🚀 WORKFLOW START - Beginning legal research")
        logger.info(f"🕐 WORKFLOW TIMESTAMP: {get_current_timestamp()}")
        logger.info(f"👤 USER: {user_id}")
        logger.info(f"🔗 THREAD: {thread_id or user_id}")
        logger.info(f"❓ QUERY: {query}")
        
        if not self.tools:
            self.setup_tools()
        if not self.graph:
            self.build_graph()
        
        config = {"configurable": {"thread_id": thread_id or user_id}}
        
        initial_state = {
            "messages": [HumanMessage(
                content=query,
                additional_kwargs={
                    "role": "user",
                    "sender": "user", 
                    "created_at": get_current_timestamp(),
                    "user_id": user_id,
                    "message_type": "query"
                }
            )],
            "original_query": query,
            "tools_used": [],
            "najir_response": "",
            "act_response": "",
            "najir_summary_response": "",
            "najir_sources": [],
            "act_sources": [],
            "najir_summary_sources": [],
            "final_response": "",
            "start_time": workflow_start,
            "step_times": {}
        }
        
        # Execute workflow
        result = await self.graph.ainvoke(initial_state, config=config)
        
        workflow_time = time.time() - workflow_start
        
        # Save to chat history with all metadata
        final_response = result.get("final_response", "")
        tools_used = result.get("tools_used", [])
        step_times = result.get("step_times", {})
        step_times["total_workflow"] = workflow_time
        
        await self.chat_manager.save_interaction(
            user_id=user_id, 
            query=query, 
            response=final_response,
            tools_used=tools_used,
            step_times=step_times
        )
        
        logger.info(f"🏁 WORKFLOW COMPLETE - Total time: {workflow_time:.2f}s")
        logger.info(f"🕐 WORKFLOW END TIMESTAMP: {get_current_timestamp()}")
        logger.info(f"🛠️  TOOLS USED: {', '.join(tools_used)}")
        
        return result

@router.post("/agent")
async def legal_agent_chat(
    query: str = Query(..., description="The legal query to process"),
    thread_id: Optional[str] = Query(None, description="Optional thread ID for conversation continuity"),
    current_user: CurrentUser = Depends(get_tenant_info)
):
    """
    Legal research agent with MongoDB persistence and proper message structure
    
    - **query**: The legal question or query in Nepali or English
    - **thread_id**: Optional thread ID for maintaining conversation context
    """
    request_start = time.time()
    request_timestamp = get_current_timestamp()
    
    logger.info("📡 API REQUEST START")
    logger.info(f"🕐 REQUEST TIMESTAMP: {request_timestamp}")
    logger.info(f"👤 USER: {current_user.user.id}")
    logger.info(f"❓ QUERY: {query}")
    logger.info(f"🔗 THREAD: {thread_id}")
    
    try:
        # Decode URL-encoded query if needed
        decoded_query = urllib.parse.unquote(query)
        logger.info(f"🔤 DECODED QUERY: {decoded_query}")
        
        agent = LegalAgent(current_user)
        result = await agent.run(decoded_query, thread_id)
        
        request_time = time.time() - request_start
        
        # Process najir sources
        najir_sources: Dict[str, Any] = await _process_sources(
            result.get("najir_sources", []), 
            current_user
        )
        
        response_data = {
            "final_response": result.get("final_response", ""),
            "najir_response": result.get("najir_response", ""),
            "act_response": result.get("act_response", ""),
            "najir_sources": najir_sources.get("sources", []),
            "act_sources": result.get("act_sources", []),
            "suggested_sources": result.get("najir_summary_sources", []),
            "total_filtered": len(result.get("najir_sources", [])) + len(result.get("act_sources", [])),
            "total_suggestions": len(result.get("najir_summary_sources", [])),
            "tools_used": result.get("tools_used", []),
            "step_times": result.get("step_times", {}),
            "metadata": {
                "request_timestamp": request_timestamp,
                "response_timestamp": get_current_timestamp(),
                "total_request_time": request_time,
                "user_id": str(current_user.user.id),
                "thread_id": thread_id,
                "query_decoded": decoded_query,
                "workflow_completed": True
            },
            "status": "success"
        }
        
        logger.info(f"✅ API SUCCESS - Request completed in {request_time:.2f}s")
        logger.info(f"🕐 RESPONSE TIMESTAMP: {get_current_timestamp()}")
        
        return response_data
        
    except Exception as e:
        request_time = time.time() - request_start
        error_timestamp = get_current_timestamp()
        
        logger.error(f"❌ API ERROR - Failed after {request_time:.2f}s: {e}")
        logger.error(f"🕐 ERROR TIMESTAMP: {error_timestamp}")
        
        return {
            "final_response": f"Error processing your request: {str(e)}",
            "najir_response": "",
            "act_response": "",
            "najir_sources": [],
            "act_sources": [],
            "suggested_sources": [],
            "total_filtered": 0,
            "total_suggestions": 0,
            "tools_used": [],
            "step_times": {},
            "metadata": {
                "request_timestamp": request_timestamp,
                "error_timestamp": error_timestamp,
                "total_request_time": request_time,
                "user_id": str(current_user.user.id),
                "thread_id": thread_id,
                "query_decoded": urllib.parse.unquote(query),
                "workflow_completed": False,
                "error": str(e)
            },
            "status": "error"
        }